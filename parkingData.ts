// Sample GeoJSON data for San Francisco parking zones
// In a production app, these would be loaded from actual GeoJSON files

export const permitZonesData = [
  {
    type: 'Feature',
    properties: {
      rpparea1: 'Zone A',
      regulation: 'Residential Permit Parking - Mon-Fri 8AM-6PM',
      exceptions: 'Sundays and holidays exempt',
      hours: '8:00 AM - 6:00 PM',
      days: 'Monday through Friday'
    },
    geometry: {
      type: 'MultiLineString',
      coordinates: [
        [
          [-122.4194, 37.7749],
          [-122.4184, 37.7759],
          [-122.4174, 37.7769],
          [-122.4164, 37.7759],
          [-122.4174, 37.7749],
          [-122.4194, 37.7749]
        ]
      ]
    }
  },
  {
    type: 'Feature',
    properties: {
      rpparea1: 'Zone B',
      regulation: 'Residential Permit Parking - Mon-Sat 9AM-5PM',
      exceptions: 'Sundays exempt',
      hours: '9:00 AM - 5:00 PM',
      days: 'Monday through Saturday'
    },
    geometry: {
      type: 'MultiLineString',
      coordinates: [
        [
          [-122.4144, 37.7779],
          [-122.4134, 37.7789],
          [-122.4124, 37.7799],
          [-122.4114, 37.7789],
          [-122.4124, 37.7779],
          [-122.4144, 37.7779]
        ]
      ]
    }
  },
  {
    type: 'Feature',
    properties: {
      rpparea1: 'Zone C',
      regulation: 'Residential Permit Parking - Daily 7AM-7PM',
      exceptions: 'Major holidays exempt',
      hours: '7:00 AM - 7:00 PM',
      days: 'Daily'
    },
    geometry: {
      type: 'MultiLineString',
      coordinates: [
        [
          [-122.4244, 37.7719],
          [-122.4234, 37.7729],
          [-122.4224, 37.7739],
          [-122.4214, 37.7729],
          [-122.4224, 37.7719],
          [-122.4244, 37.7719]
        ]
      ]
    }
  },
  {
    type: 'Feature',
    properties: {
      rpparea1: 'Zone D',
      regulation: 'Residential Permit Parking - Mon-Fri 10AM-4PM',
      exceptions: 'Weekends and holidays exempt',
      hours: '10:00 AM - 4:00 PM',
      days: 'Monday through Friday'
    },
    geometry: {
      type: 'MultiLineString',
      coordinates: [
        [
          [-122.4094, 37.7809],
          [-122.4084, 37.7819],
          [-122.4074, 37.7829],
          [-122.4064, 37.7819],
          [-122.4074, 37.7809],
          [-122.4094, 37.7809]
        ]
      ]
    }
  }
];

export const paidParkingData = [
  {
    type: 'Feature',
    properties: {
      street_name: 'Market Street',
      meter_type: 'Smart Meter',
      cap_color: 'Green',
      rate: '$2.50/hour',
      time_limit: '2 hours'
    },
    geometry: {
      type: 'Point',
      coordinates: [-122.4194, 37.7849]
    }
  },
  {
    type: 'Feature',
    properties: {
      street_name: 'Union Square',
      meter_type: 'Smart Meter',
      cap_color: 'Red',
      rate: '$4.00/hour',
      time_limit: '1 hour'
    },
    geometry: {
      type: 'Point',
      coordinates: [-122.4084, 37.7879]
    }
  },
  {
    type: 'Feature',
    properties: {
      street_name: 'Lombard Street',
      meter_type: 'Traditional Meter',
      cap_color: 'Blue',
      rate: '$3.00/hour',
      time_limit: '2 hours'
    },
    geometry: {
      type: 'Point',
      coordinates: [-122.4194, 37.8019]
    }
  },
  {
    type: 'Feature',
    properties: {
      street_name: 'Fisherman\'s Wharf',
      meter_type: 'Smart Meter',
      cap_color: 'Yellow',
      rate: '$3.50/hour',
      time_limit: '3 hours'
    },
    geometry: {
      type: 'Point',
      coordinates: [-122.4144, 37.8089]
    }
  },
  {
    type: 'Feature',
    properties: {
      street_name: 'Castro Street',
      meter_type: 'Smart Meter',
      cap_color: 'Green',
      rate: '$2.00/hour',
      time_limit: '2 hours'
    },
    geometry: {
      type: 'Point',
      coordinates: [-122.4344, 37.7609]
    }
  }
];